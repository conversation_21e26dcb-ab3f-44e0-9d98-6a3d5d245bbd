<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-21T00:51:34.273379" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-605_ATM_DETAIL_Verify_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable 'BROWSER' set to value 'chrome'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'Alternative_Physical_Channels_QA/vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<test id="s1-t1" name="Validate Existance of All Frontend ATMs" line="38">
<kw name="ATM Existance Validation">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.867969" level="INFO">Set test documentation to:
Validate Existance of All Frontend ATMs</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-21T00:51:38.867969" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.875484" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:38.875484" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-21T00:51:38.875484" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-21T00:51:38.875484" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.875484" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-21T00:51:38.875484" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-21T00:51:38.875484" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.875484" level="INFO">${PROCESS_NAME_LowerCase} = chrome</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-21T00:51:38.875484" level="INFO">${handle} = chrome.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-21T00:51:39.085536" level="INFO">Running command 'taskkill /F /IM chrome.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-21T00:51:39.854639" level="INFO">${rc_code} = 0</msg>
<msg time="2025-06-21T00:51:39.854639" level="INFO">${output} = SUCCESS: The process "chrome.exe" with PID 17116 has been terminated.
SUCCESS: The process "chrome.exe" with PID 23564 has been terminated.
SUCCESS: The process "chrome.exe" with PID 24628 has been te...</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.979155"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-21T00:51:39.854639" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-21T00:51:38.875484" elapsed="0.979155"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:39.854639" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-21T00:51:39.854639" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T00:51:39.867159" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T00:51:39.854639" elapsed="0.012520"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-21T00:51:39.867159" level="INFO">${browser_name} = CHROME</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-21T00:51:39.867159" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.015635"/>
</branch>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.015635"/>
</if>
<status status="NOT RUN" start="2025-06-21T00:51:39.867159" elapsed="0.015635"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.882794" level="INFO">${chromedriver_path} = C:/bin/chromedriver.exe</msg>
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.882794" level="INFO">${chrome_options} = &lt;selenium.webdriver.chrome.options.Options object at 0x000002167F52E360&gt;</msg>
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.882794" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.882794" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.882794" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.882794" level="INFO">${prefs} = {'useAutomationExtension': False, 'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'd...</msg>
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T00:51:39.882794" elapsed="0.015620"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T00:51:39.898414" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T00:51:39.898414" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T00:51:39.898414" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T00:51:39.898414" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.898414" level="INFO">${dc} = {'browserName': 'chrome'}</msg>
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-21T00:51:39.898414" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<msg time="2025-06-21T00:51:39.898414" level="INFO">${Options} = {'browserName': 'chrome', 'pageLoadStrategy': &lt;PageLoadStrategy.normal: 'normal'&gt;, 'goog:chromeOptions': {'prefs': {'useAutomationExtension': False, 'download.default_directory': 'C:\\Users\\<USER>\\...</msg>
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-21T00:51:39.898414" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-21T00:51:39.898414" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-21T00:51:39.898414" level="INFO">Opening browser 'chrome' to base url 'about:blank'.</msg>
<msg time="2025-06-21T00:52:02.373281" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg time="2025-06-21T00:52:02.373281" level="FAIL">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="FAIL" start="2025-06-21T00:51:39.898414" elapsed="22.584316">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</kw>
<status status="FAIL" start="2025-06-21T00:51:39.882794" elapsed="22.599936">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</branch>
<status status="FAIL" start="2025-06-21T00:51:39.867159" elapsed="22.615571">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</if>
<status status="FAIL" start="2025-06-21T00:51:38.875484" elapsed="23.607246">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</kw>
<kw name="Load" owner="Login">
<arg>${base_url}</arg>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</iter>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="FAIL" start="2025-06-21T00:51:38.875484" elapsed="23.607246">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="FAIL" start="2025-06-21T00:51:38.875484" elapsed="23.607246">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="FAIL" start="2025-06-21T00:51:38.867969" elapsed="23.614761">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</kw>
<kw name="When The user clicks on the ATM Details link" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="And The user lands on the ATM Details pages" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="Then The user reads number of ATM rows on Frontend" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<kw name="And The user compares Frontend ATM Details to the Backend ATM Details" owner="ATMDetails">
<status status="NOT RUN" start="2025-06-21T00:52:02.482730" elapsed="0.000000"/>
</kw>
<arg>Validate Existance of All Frontend ATMs</arg>
<arg>VMS_UAT</arg>
<status status="FAIL" start="2025-06-21T00:51:38.867969" elapsed="23.614761">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]
</status>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<msg time="2025-06-21T00:52:02.482730" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg time="2025-06-21T00:52:02.482730" level="FAIL">No browser is open.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-21T00:52:02.482730" elapsed="0.038600">No browser is open.</status>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-21T00:52:02.521330" elapsed="0.000954"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-21T00:52:02.523557" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<msg time="2025-06-21T00:52:02.523557" level="INFO">Cannot capture screenshot because no browser is open.</msg>
<msg time="2025-06-21T00:52:02.524365" level="FAIL">No browser is open.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="FAIL" start="2025-06-21T00:52:02.523037" elapsed="0.001456">No browser is open.</status>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="FAIL" start="2025-06-21T00:52:02.524493" elapsed="0.180763">Execution terminated by signal</status>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="NOT RUN" start="2025-06-21T00:52:02.705256" elapsed="0.000000"/>
</kw>
<status status="FAIL" start="2025-06-21T00:52:02.482730" elapsed="0.223549">Several failures occurred:

1) No browser is open.

2) No browser is open.

3) Execution terminated by signal</status>
</kw>
<doc>Validate Existance of All Frontend ATMs</doc>
<tag>ATM DETAILS</tag>
<tag>VMS HEALTHCHECK</tag>
<status status="FAIL" start="2025-06-21T00:51:38.867969" elapsed="23.838310">SessionNotCreatedException: Message: session not created: probably user data directory is already in use, please specify a unique value for --user-data-dir argument, or don't use --user-data-dir
Stacktrace:
	GetHandleVerifier [0x00007FF7CA10A145+76773]
	GetHandleVerifier [0x00007FF7CA10A1A0+76864]
	(No symbol) [0x00007FF7C9EC8F7A]
	(No symbol) [0x00007FF7C9F05B82]
	(No symbol) [0x00007FF7C9F01B5D]
	(No symbol) [0x00007FF7C9F55155]
	(No symbol) [0x00007FF7C9F54710]
	(No symbol) [0x00007FF7C9F47133]
	(No symbol) [0x00007FF7C9F104D1]
	(No symbol) [0x00007FF7C9F11263]
	GetHandleVerifier [0x00007FF7CA3CA8ED+2962317]
	GetHandleVerifier [0x00007FF7CA3C4EC2+2939234]
	GetHandleVerifier [0x00007FF7CA3E2FF3+3062419]
	GetHandleVerifier [0x00007FF7CA124B9A+185914]
	GetHandleVerifier [0x00007FF7CA12C78F+217647]
	GetHandleVerifier [0x00007FF7CA112A44+111844]
	GetHandleVerifier [0x00007FF7CA112BF2+112274]
	GetHandleVerifier [0x00007FF7CA0F8A79+5401]
	BaseThreadInitThunk [0x00007FFFD957259D+29]
	RtlUserThreadStart [0x00007FFFDA6EAF58+40]


Also teardown failed:
Several failures occurred:

1) No browser is open.

2) No browser is open.

3) Execution terminated by signal</status>
</test>
<doc>Validate Details of All ATMs</doc>
<status status="FAIL" start="2025-06-21T00:51:34.365787" elapsed="28.344478"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="0" fail="1" skip="0">ATM DETAILS</stat>
<stat pass="0" fail="1" skip="0">VMS HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="0" fail="1" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-21T00:51:34.354035" level="ERROR">Taking listener 'Alternative_Physical_Channels_QA/common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 198: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 228: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 259: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 299: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 344: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 356: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 399: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 690: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.299778" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 721: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 752: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 788: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 798: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 809: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 827: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 867: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 887: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 914: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 945: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.315347" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1051: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.330924" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1305: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.346577" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1416: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.425329" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.425329" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.539710" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:37.539710" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 389: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-21T00:51:38.844102" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-21T00:51:38.859729" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-21T00:51:39.898414" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
</errors>
</robot>
