server-api: 2025-06-20 12:24:42 UTC pid: 11648 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.1.api']

server-api: 2025-06-20 12:24:42 UTC pid: 11648 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-20 12:24:42 UTC pid: 11648 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-20 12:24:42 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 11648

server-api: 2025-06-20 12:24:43 UTC pid: 11648 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-20 12:24:43 UTC pid: 11648 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-20 12:24:43 UTC pid: 11648 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-20 12:24:57 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 5, method: hover

server-api: 2025-06-20 12:24:57 UTC pid: 11648 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl.workspace_symbols


Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\workspace_symbols.py", line 197, in iter_symbols_caches
    for _uri, symbols_cache in workspace_indexer.iter_uri_and_symbols_cache(
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\robot_workspace.py", line 528, in iter_uri_and_symbols_cache
    symbols_cache = _compute_symbols_from_ast(ctx)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\robot_workspace.py", line 160, in _compute_symbols_from_ast
    test_info = list_tests(completion_context)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_lens.py", line 19, in list_tests
    completion_context.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\completion_context.py", line 211, in check_cancelled
    self._monitor.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\monitor.py", line 21, in check_cancelled
    raise JsonRpcRequestCancelled()
robocorp_ls_core.jsonrpc.exceptions.JsonRpcRequestCancelled: None
server-api: 2025-06-20 12:24:57 UTC pid: 11648 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl._symbols_cache
Exception computing symbols cache reverse index.

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\_symbols_cache.py", line 184, in _compute_new_symbols_cache_reverse_index_state
    for symbols_cache in it:
                         ^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\workspace_symbols.py", line 197, in iter_symbols_caches
    for _uri, symbols_cache in workspace_indexer.iter_uri_and_symbols_cache(
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\robot_workspace.py", line 528, in iter_uri_and_symbols_cache
    symbols_cache = _compute_symbols_from_ast(ctx)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\robot_workspace.py", line 160, in _compute_symbols_from_ast
    test_info = list_tests(completion_context)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_lens.py", line 19, in list_tests
    completion_context.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\completion_context.py", line 211, in check_cancelled
    self._monitor.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\monitor.py", line 21, in check_cancelled
    raise JsonRpcRequestCancelled()
robocorp_ls_core.jsonrpc.exceptions.JsonRpcRequestCancelled: None
server-api: 2025-06-20 12:28:39 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 16, method: hover

server-api: 2025-06-20 12:29:37 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 37, method: hover

server-api: 2025-06-20 12:29:37 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 39, method: code_action

server-api: 2025-06-20 12:29:37 UTC pid: 11648 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl.workspace_symbols


Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\workspace_symbols.py", line 197, in iter_symbols_caches
    for _uri, symbols_cache in workspace_indexer.iter_uri_and_symbols_cache(
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\robot_workspace.py", line 486, in iter_uri_and_symbols_cache
    context.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\completion_context.py", line 211, in check_cancelled
    self._monitor.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\monitor.py", line 21, in check_cancelled
    raise JsonRpcRequestCancelled()
robocorp_ls_core.jsonrpc.exceptions.JsonRpcRequestCancelled: None
server-api: 2025-06-20 13:53:37 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 77, method: hover

server-api: 2025-06-20 19:40:26 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 86, method: hover

server-api: 2025-06-20 19:46:21 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 96, method: hover

server-api: 2025-06-20 19:46:22 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 97, method: hover

server-api: 2025-06-20 19:46:24 UTC pid: 11648 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
server-api: 2025-06-20 19:46:24 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 99, method: findDefinition

server-api: 2025-06-20 19:46:25 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 104, method: hover

server-api: 2025-06-20 19:46:26 UTC pid: 11648 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
server-api: 2025-06-20 19:47:11 UTC pid: 11648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 150, method: hover

