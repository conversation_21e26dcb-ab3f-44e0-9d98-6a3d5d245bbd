<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-20T13:14:30.639392" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ADMIN_USER_MANAGEMENT\RAC29a_TC_340_Add_New_User_Blank_Fields_User_Management.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:36.357425" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:36.357425" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'Bin_Tables.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:36.357425" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.357425" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:36.372143" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-20T13:14:36.371130" elapsed="0.002014"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.371130" elapsed="0.002014"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:36.374348" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'vms/data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-20T13:14:36.374348" elapsed="0.000000"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.373144" elapsed="0.001204"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.375828" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-20T13:14:36.375360" elapsed="0.000468"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.374348" elapsed="0.001480"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.376836" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-20T13:14:36.375828" elapsed="0.001008"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.375828" elapsed="0.001008"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-20T13:14:36.377832" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:36.377832" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:14:36.376836" elapsed="0.000996"/>
</branch>
<status status="PASS" start="2025-06-20T13:14:36.376836" elapsed="0.000996"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.378833" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-20T13:14:36.378833" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.377832" elapsed="0.001001"/>
</kw>
<status status="PASS" start="2025-06-20T13:14:36.357425" elapsed="0.022406"/>
</kw>
<test id="s1-t1" name="Add New User- Blank Fields- User Management 'Browse' Role Test Case: Do not populate the 'Username' field" line="38">
<kw name="VMS User Creation - Negative Testing">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.381979" level="INFO">Set test documentation to:
Create a VMS user with 'Browse' Role without populating the Username.</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-20T13:14:36.381979" elapsed="0.001002"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.383982" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-20T13:14:36.382981" elapsed="0.001001"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-20T13:14:36.383982" elapsed="0.001004"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-20T13:14:36.384986" elapsed="0.000997"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:36.390527" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-20T13:14:36.390527" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-20T13:14:36.390527" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-20T13:14:36.389513" elapsed="0.001014"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:36.391529" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:14:36.391529" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.391529" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-20T13:14:36.391529" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-20T13:14:36.391529" elapsed="0.000997"/>
</branch>
<status status="PASS" start="2025-06-20T13:14:36.390527" elapsed="0.001999"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-20T13:14:36.412018" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-20T13:14:36.412018" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-20T13:14:36.392526" elapsed="0.019492"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.412018" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.412018" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-20T13:14:36.412018" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.000000"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-20T13:14:36.667795" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-20T13:14:37.365151" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-20T13:14:37.365151" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.953133"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-20T13:14:37.365151" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="0.953133"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x00000221A609B4D0&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x00000221A609CFB0&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-20T13:14:37.365151" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-20T13:14:37.365151" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-20T13:14:37.365151" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="4.291250"/>
</kw>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="4.291250"/>
</branch>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="4.291250"/>
</if>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="4.291250"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.015624"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-20T13:14:41.672025" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:14:41.656401" elapsed="0.015624"/>
</branch>
<status status="PASS" start="2025-06-20T13:14:37.365151" elapsed="4.306874"/>
</if>
<status status="PASS" start="2025-06-20T13:14:36.412018" elapsed="5.260007"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-20T13:14:41.672025" elapsed="0.095833"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-20T13:14:41.767858" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-20T13:14:41.767858" elapsed="2.718337"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:41.767858" elapsed="2.718337"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-20T13:14:54.486706" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-20T13:14:44.486195" elapsed="10.000511"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-20T13:14:54.486706" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-20T13:14:54.508761" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:14:54.486706" elapsed="0.022055"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-20T13:14:54.508761" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-20T13:15:04.509238" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-20T13:14:54.508761" elapsed="10.000477"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:04.509238" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-20T13:15:04.524544" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:04.509238" elapsed="0.015306"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-20T13:15:04.525546" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-20T13:15:09.525997" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-20T13:15:04.525546" elapsed="5.000451"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:09.525997" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-20T13:15:09.563670" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:09.525997" elapsed="0.037673"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-20T13:15:09.564661" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-20T13:14:41.672025" elapsed="27.893633"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:09.675394" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-20T13:15:09.566657" elapsed="0.108737"/>
</kw>
<msg time="2025-06-20T13:15:09.675394" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-20T13:15:09.566657" elapsed="0.108737"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:09.675394" level="INFO">Typing text 'AB038N8' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:09.675394" elapsed="0.122940"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-20T13:15:09.798334" elapsed="0.027114"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:09.825448" level="INFO">Typing text '67355870@SitholeBrother' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:09.825448" elapsed="0.165387"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:09.990835" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:09.990835" elapsed="0.127697"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-20T13:15:12.118993" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-20T13:15:10.118532" elapsed="2.000461"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:56.090982" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-1.png"&gt;&lt;img src="selenium-screenshot-1.png" width="800px"&gt;&lt;/a&gt;</msg>
<msg time="2025-06-20T13:15:56.090982" level="FAIL">Element with locator 'name=txtUsername' not found.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="FAIL" start="2025-06-20T13:15:12.118993" elapsed="44.038953">Element with locator 'name=txtUsername' not found.</status>
</kw>
<msg time="2025-06-20T13:15:56.157946" level="INFO">${User_Name_Element_Visible} = False</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-20T13:15:12.118993" elapsed="44.038953"/>
</kw>
<status status="PASS" start="2025-06-20T13:15:09.675394" elapsed="46.482552"/>
</iter>
<status status="PASS" start="2025-06-20T13:15:09.675394" elapsed="46.482552"/>
</while>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------ User successfully accessed VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-20T13:15:56.157946" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:56.299452" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="selenium-screenshot-2.png"&gt;&lt;img src="selenium-screenshot-2.png" width="800px"&gt;&lt;/a&gt;</msg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-20T13:15:56.157946" elapsed="0.141506"/>
</kw>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<status status="PASS" start="2025-06-20T13:14:36.386781" elapsed="79.912671"/>
</kw>
<arg>'${system}' == 'Windows'</arg>
<arg>VMS Windows system Login</arg>
<arg>${URL}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Linux system login</arg>
<arg>${URL}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-20T13:14:36.385983" elapsed="79.914423"/>
</kw>
<arg>${TEST_ENVIRONMENT}</arg>
<status status="PASS" start="2025-06-20T13:14:36.382981" elapsed="79.917425"/>
</kw>
<kw name="When The user navigates to Admin - User Management" owner="UserManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:56.317587" level="INFO">Current page contains text 'Dashboard'.</msg>
<arg>Dashboard</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-20T13:15:56.300406" elapsed="0.017181"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:56.441410" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="VMS_Landing_Page.png"&gt;&lt;img src="VMS_Landing_Page.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>VMS_Landing_Page.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-20T13:15:56.317587" elapsed="0.123823"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:56.441410" level="INFO">Clicking element 'xpath=//*[text()[normalize-space(.)='Admin']]'.</msg>
<arg>${ADMIN_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:56.441410" elapsed="0.101584"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:56.585082" level="INFO">Current page contains text 'User Management'.</msg>
<arg>User Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-20T13:15:56.542994" elapsed="0.042088"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:56.586089" level="INFO">Clicking element 'xpath=//a[text()[normalize-space(.)='User Management']]'.</msg>
<arg>${USER_MANAGEMENT_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:56.585082" elapsed="0.459061"/>
</kw>
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:57.081722" level="INFO">Current page contains text 'User Management'.</msg>
<arg>User Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-20T13:15:57.044143" elapsed="0.037579"/>
</kw>
<status status="PASS" start="2025-06-20T13:15:56.300406" elapsed="0.782319"/>
</kw>
<kw name="And The user Adds a new VMS User while leaving some fields blank" owner="UserManagement">
<kw name="Page Should Contain" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:57.115167" level="INFO">Current page contains text 'User Management'.</msg>
<arg>User Management</arg>
<doc>Verifies that current page contains ``text``.</doc>
<status status="PASS" start="2025-06-20T13:15:57.084725" elapsed="0.030442"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:57.117171" level="INFO">Clicking element 'xpath=//a[@id='btnAddUser']'.</msg>
<arg>${ADD_NEW_USER_LINK}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:57.116170" elapsed="0.123156"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-20T13:15:59.239753" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-20T13:15:57.239659" elapsed="2.000094"/>
</kw>
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:59.290086" level="INFO">Element 'xpath=//*[contains(@class,'modal-title')][text()[normalize-space(.)='Admin - Add New User']]' is displayed.</msg>
<arg>${ADD_NEW_USER_POP_UP_TEXT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-20T13:15:59.239753" elapsed="0.050333"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:59.290823" level="INFO">Typing text '' into text field 'xpath=//input[@id='MainContent_txtAdd_Username'][@name='ctl00$MainContent$txtAdd_Username']'.</msg>
<arg>${ADD_NEW_USER_USERNAME_INPUT}</arg>
<arg>${USER_NAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:59.290823" elapsed="0.183654"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:59.474477" level="INFO">Typing text 'Automation User Browser' into text field 'xpath=//input[@id='MainContent_txtAdd_Name'][@name='ctl00$MainContent$txtAdd_Name']'.</msg>
<arg>${ADD_NEW_USER_NAME_INPUT}</arg>
<arg>${NAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:59.474477" elapsed="0.218993"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-20T13:15:59.693470" level="INFO">${role} = BROWSE</msg>
<var>${role}</var>
<arg>${USER_ROLE}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-20T13:15:59.693470" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="&quot;${role}&quot; == &quot;BROWSE&quot;">
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:59.694726" level="INFO">Clicking element 'xpath=//input[@id='MainContent_rdbAdd_Browse'][@name='ctl00$MainContent$group'][@type='radio']'.</msg>
<arg>${ADD_NEW_USER_BROWSE_RADIO}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:59.694726" elapsed="0.164181"/>
</kw>
<status status="PASS" start="2025-06-20T13:15:59.693470" elapsed="0.165437"/>
</branch>
<branch type="ELSE IF" condition="&quot;${role}&quot; == &quot;USER&quot;">
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${ADD_NEW_USER_USER_RADIO}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-20T13:15:59.858907" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:15:59.858907" elapsed="0.000000"/>
</branch>
<branch type="ELSE IF" condition="&quot;${role}&quot; == &quot;SUPERVISOR&quot;">
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${ADD_NEW_USER_SUPERVISOR_RADIO}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-20T13:15:59.858907" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:15:59.858907" elapsed="0.000000"/>
</branch>
<branch type="ELSE IF" condition="&quot;${role}&quot; == &quot;ADMINISTRATOR&quot;">
<kw name="Click Element" owner="SeleniumLibrary">
<arg>${ADD_NEW_USER_ADMINISTRATOR_RADIO}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="NOT RUN" start="2025-06-20T13:15:59.858907" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-20T13:15:59.858907" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-20T13:15:59.693470" elapsed="0.165437"/>
</if>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:59.858907" level="INFO">Clicking element 'xpath=//button[@id='btnAdd_VendorEmail'][text()[normalize-space(.)='Submit']]'.</msg>
<arg>${ADD_NEW_USER_SUBMIT_BTN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:15:59.858907" elapsed="0.100055"/>
</kw>
<arg>${USER_NAME}</arg>
<arg>${NAME}</arg>
<arg>${USER_ROLE}</arg>
<status status="PASS" start="2025-06-20T13:15:57.083728" elapsed="2.875234"/>
</kw>
<kw name="Then The exepcted error message must be displayed" owner="UserManagement">
<kw name="Retrieve Validation Message from the Browser Using Java Script" owner="common_keywords">
<kw name="Execute Javascript" owner="SeleniumLibrary">
<msg time="2025-06-20T13:15:59.958962" level="INFO">Executing JavaScript:
return document.querySelector('input:invalid') ? document.querySelector('input:invalid').validationMessage : 'No validation message found';
Without any arguments.</msg>
<msg time="2025-06-20T13:15:59.979610" level="INFO">${validation_message} = Please fill out this field.</msg>
<var>${validation_message}</var>
<arg>return document.querySelector('input:invalid') ? document.querySelector('input:invalid').validationMessage : 'No validation message found';</arg>
<doc>Executes the given JavaScript code with possible arguments.</doc>
<status status="PASS" start="2025-06-20T13:15:59.958962" elapsed="0.020648"/>
</kw>
<return>
<value>${validation_message}</value>
<status status="PASS" start="2025-06-20T13:15:59.979610" elapsed="0.000000"/>
</return>
<msg time="2025-06-20T13:15:59.979610" level="INFO">${validation_message} = Please fill out this field.</msg>
<var>${validation_message}</var>
<status status="PASS" start="2025-06-20T13:15:59.958962" elapsed="0.020648"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-20T13:15:59.979610" level="INFO">Please fill out this field.</msg>
<arg>${validation_message}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-20T13:15:59.979610" elapsed="0.000000"/>
</kw>
<kw name="Capture Page Screenshot" owner="SeleniumLibrary">
<msg time="2025-06-20T13:16:00.177819" level="INFO" html="true">&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td colspan="3"&gt;&lt;a href="NEW_USER_DETAILS_POPULATED.png"&gt;&lt;img src="NEW_USER_DETAILS_POPULATED.png" width="800px"&gt;&lt;/a&gt;</msg>
<arg>NEW_USER_DETAILS_POPULATED.png</arg>
<doc>Takes a screenshot of the current page and embeds it into a log file.</doc>
<status status="PASS" start="2025-06-20T13:15:59.979610" elapsed="0.198209"/>
</kw>
<kw name="Click Element" owner="SeleniumLibrary">
<msg time="2025-06-20T13:16:00.177819" level="INFO">Clicking element 'xpath=//*[@id='addUserModal']/div/div/form/div[contains(@class,'modal-footer')]/div/div[contains(@class,'col-md-4')][contains(@class,'form-group')]/input'.</msg>
<arg>${ADD_NEW_USER_CANCEL_BTN}</arg>
<doc>Click the element identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:16:00.177819" elapsed="0.164394"/>
</kw>
<kw name="Should Be Equal" owner="BuiltIn">
<arg>${validation_message}</arg>
<arg>${EXPECTED_ERROR}</arg>
<doc>Fails if the given objects are unequal.</doc>
<status status="PASS" start="2025-06-20T13:16:00.343177" elapsed="0.000285"/>
</kw>
<arg>${EXPECTED_ERROR}</arg>
<status status="PASS" start="2025-06-20T13:15:59.958962" elapsed="0.384500"/>
</kw>
<arg>Create a VMS user with 'Browse' Role without populating the Username.</arg>
<arg>VMS_UAT</arg>
<arg>Automation User Browser</arg>
<arg>Browse</arg>
<arg>Please fill out this field.</arg>
<status status="PASS" start="2025-06-20T13:14:36.381643" elapsed="83.961819"/>
</kw>
<kw name="The user logs out of VMS" owner="Logout" type="TEARDOWN">
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-20T13:16:00.343462" elapsed="0.365368"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>------------------------------------ Logging out of VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-20T13:16:00.708830" elapsed="0.000000"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-20T13:16:00.708830" level="INFO">Clicking button 'name=ctl00$btnLogout'.</msg>
<arg>${VMS_LOGOUT_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-20T13:16:00.708830" elapsed="0.198054"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-20T13:16:03.908154" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-20T13:16:00.907403" elapsed="3.000751"/>
</kw>
<kw name="Close Browser" owner="SeleniumLibrary">
<doc>Closes the current browser.</doc>
<status status="PASS" start="2025-06-20T13:16:03.908154" elapsed="2.853471"/>
</kw>
<status status="PASS" start="2025-06-20T13:16:00.343462" elapsed="6.418163"/>
</kw>
<doc>Create a VMS user with 'Browse' Role without populating the Username.</doc>
<tag>HEALTHCHECK_STATUS</tag>
<tag>Login</tag>
<tag>VMS_HEALTHCHECK</tag>
<status status="PASS" start="2025-06-20T13:14:36.379831" elapsed="90.382801"/>
</test>
<doc>Add new User to VMS</doc>
<status status="PASS" start="2025-06-20T13:14:30.722948" elapsed="96.039684"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">HEALTHCHECK_STATUS</stat>
<stat pass="1" fail="0" skip="0">Login</stat>
<stat pass="1" fail="0" skip="0">VMS_HEALTHCHECK</stat>
</tag>
<suite>
<stat name="VMS Portal" id="s1" pass="1" fail="0" skip="0">VMS Portal</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-20T13:14:30.622059" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ADMIN_USER_MANAGEMENT\RAC29a_TC_340_Add_New_User_Blank_Fields_User_Management.robot' on line 37: Singular section headers like '*Test Case*' are deprecated. Use plural format like '*** Test Cases ***' instead.</msg>
<msg time="2025-06-20T13:14:30.722948" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-20T13:14:33.394615" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-20T13:14:33.410641" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-20T13:14:36.175265" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-20T13:14:36.175265" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-20T13:14:36.309458" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-20T13:14:36.309458" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 389: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-20T13:14:37.365151" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-20T13:14:37.365151" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-20T13:14:54.486706" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-20T13:15:04.509238" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-20T13:15:09.525997" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
</errors>
</robot>
