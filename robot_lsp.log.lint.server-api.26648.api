server-api: 2025-06-19 12:25:06 UTC pid: 26648 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.lint.api']

server-api: 2025-06-19 12:25:06 UTC pid: 26648 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-19 12:25:06 UTC pid: 26648 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-19 12:25:06 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 26648

server-api: 2025-06-19 12:25:07 UTC pid: 26648 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-19 12:25:07 UTC pid: 26648 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-19 12:25:07 UTC pid: 26648 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-19 12:25:26 UTC pid: 26648 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_0, started 1004)>

self: <Thread(Thread-27 (_readerthread), started daemon 23620)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 479, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

server-api: 2025-06-19 12:25:35 UTC pid: 26648 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_1, started 3424)>

self: <Thread(Thread-42 (_readerthread), started daemon 16308)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 479, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

server-api: 2025-06-19 12:25:35 UTC pid: 26648 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_2, started 23880)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 544, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1028, in _cached_create_libspec
   with acquire_mutex(libspec_filename):  # Could fail.
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 137, in __enter__
   return next(self.gen)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 51, in _timed_acquire_mutex_for_spec_filename
   ctx = timed_acquire_mutex(_get_libspec_mutex_name(spec_filename), timeout=30)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\system_mutex.py", line 382, in timed_acquire_mutex
   time.sleep(sleep_time)

=============================== END Thread Dump ===============================

server-api: 2025-06-19 12:25:35 UTC pid: 26648 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_3, started 21488)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 479, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1370, in get_library_doc_or_error
   return self.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1297, in get_library_doc_or_error
   for lib_info in self.iter_lib_info(builtin=builtin):
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 821, in iter_lib_info
   for libinfo in self._iter_lib_info(builtin):
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 876, in _iter_lib_info
   ] = _load_lib_info(self, canonical_spec_filename, can_regenerate)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 107, in _load_lib_info
   libdoc_and_mtime = _load_library_doc_and_mtime(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 79, in _load_library_doc_and_mtime
   with ctx:
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 137, in __enter__
   return next(self.gen)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 51, in _timed_acquire_mutex_for_spec_filename
   ctx = timed_acquire_mutex(_get_libspec_mutex_name(spec_filename), timeout=30)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\system_mutex.py", line 382, in timed_acquire_mutex
   time.sleep(sleep_time)

=============================== END Thread Dump ===============================

server-api: 2025-06-19 12:25:52 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 11, method: lint

server-api: 2025-06-19 12:26:01 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 8, method: lint

server-api: 2025-06-19 12:26:21 UTC pid: 26648 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_4, started 9484)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 479, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1297, in get_library_doc_or_error
   for lib_info in self.iter_lib_info(builtin=builtin):
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 821, in iter_lib_info
   for libinfo in self._iter_lib_info(builtin):
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 876, in _iter_lib_info
   ] = _load_lib_info(self, canonical_spec_filename, can_regenerate)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 107, in _load_lib_info
   libdoc_and_mtime = _load_library_doc_and_mtime(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 79, in _load_library_doc_and_mtime
   with ctx:
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\contextlib.py", line 137, in __enter__
   return next(self.gen)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 51, in _timed_acquire_mutex_for_spec_filename
   ctx = timed_acquire_mutex(_get_libspec_mutex_name(spec_filename), timeout=30)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\system_mutex.py", line 382, in timed_acquire_mutex
   time.sleep(sleep_time)

=============================== END Thread Dump ===============================

server-api: 2025-06-19 12:26:31 UTC pid: 26648 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
server-api: 2025-06-19 14:28:54 UTC pid: 26648 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\common\\DBUtility.robot'])

server-api: 2025-06-19 14:43:33 UTC pid: 26648 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\common\\DBUtility.robot'])

server-api: 2025-06-19 14:45:22 UTC pid: 26648 - ThreadPoolExecutor-0_7 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\common\\DBUtility.robot'])

server-api: 2025-06-19 14:45:22 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 252, method: lint

server-api: 2025-06-19 14:45:22 UTC pid: 26648 - ThreadPoolExecutor-0_7 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${database_atm_no.replace(" ", "")}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:46:22 UTC pid: 26648 - ThreadPoolExecutor-0_2 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${EASTERN_CAPE_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:47:26 UTC pid: 26648 - ThreadPoolExecutor-0_6 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${NORTH_WEST_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:47:47 UTC pid: 26648 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${FREE_STATE_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:48:16 UTC pid: 26648 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${NORTH_WEST_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:48:43 UTC pid: 26648 - ThreadPoolExecutor-0_5 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${NORTH_WEST_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:48:43 UTC pid: 26648 - ThreadPoolExecutor-0_7 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${FREE_STATE_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:49:08 UTC pid: 26648 - ThreadPoolExecutor-0_3 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${EASTERN_CAPE_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:49:56 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 277, method: lint

server-api: 2025-06-19 14:49:56 UTC pid: 26648 - ThreadPoolExecutor-0_5 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${NORTHERN_CAPE_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:50:21 UTC pid: 26648 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${NORTHERN_CAPE_MAIN_CALLS_QUERY_THIS_WEEK}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 14:52:21 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 288, method: lint

server-api: 2025-06-19 14:53:34 UTC pid: 26648 - ThreadPoolExecutor-0_3 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-19 14:58:37 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 296, method: lint

server-api: 2025-06-19 15:01:51 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 303, method: lint

server-api: 2025-06-19 15:01:51 UTC pid: 26648 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${Front_End_EasternCape_This_Week}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1277, in iter_variable_references
    yield VarTokenInfo(stack, node, t, var_info)
GeneratorExit
server-api: 2025-06-19 15:02:37 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 306, method: lint

server-api: 2025-06-19 15:12:04 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 309, method: lint

server-api: 2025-06-19 15:12:35 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 312, method: lint

server-api: 2025-06-19 15:13:49 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 317, method: lint

server-api: 2025-06-19 15:28:32 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 322, method: lint

server-api: 2025-06-19 15:30:27 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 333, method: lint

server-api: 2025-06-19 15:30:44 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 336, method: lint

server-api: 2025-06-19 15:31:03 UTC pid: 26648 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 339, method: lint

