server-api: 2025-06-20 12:24:37 UTC pid: 25324 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.lint.api']

server-api: 2025-06-20 12:24:37 UTC pid: 25324 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-20 12:24:37 UTC pid: 25324 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-20 12:24:37 UTC pid: 25324 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 25324

server-api: 2025-06-20 12:24:38 UTC pid: 25324 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-20 12:24:38 UTC pid: 25324 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-20 12:24:38 UTC pid: 25324 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-20 12:26:55 UTC pid: 25324 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_0, started 26728)>

self: <Thread(Thread-67 (_readerthread), started daemon 23660)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 544, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

server-api: 2025-06-20 12:26:58 UTC pid: 25324 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
server-api: 2025-06-20 12:28:57 UTC pid: 25324 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_0, started 26728)>

self: <Thread(Thread-92 (_readerthread), started daemon 29076)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 544, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

server-api: 2025-06-20 12:29:16 UTC pid: 25324 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../../../vms/keywords/common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\vms\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\vms\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\vms\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\vms\\keywords\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\vms\\keywords\\common\\DBUtility.robot'])

server-api: 2025-06-20 19:46:37 UTC pid: 25324 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 79, method: lint

server-api: 2025-06-20 19:46:39 UTC pid: 25324 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 83, method: lint

server-api: 2025-06-20 19:46:39 UTC pid: 25324 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl.ast_utils
Unable to tokenize: ${no_results_message} or ${no_data_message} or ${empty_message}

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\ast_utils.py", line 1317, in iter_variable_references
    yield VarTokenInfo(stack, node, tok, var_info)
GeneratorExit
