server-api: 2025-06-20 11:31:55 UTC pid: 3744 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.lint.api']

server-api: 2025-06-20 11:31:55 UTC pid: 3744 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-20 11:31:55 UTC pid: 3744 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-20 11:31:55 UTC pid: 3744 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 3744

server-api: 2025-06-20 11:31:56 UTC pid: 3744 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-20 11:31:56 UTC pid: 3744 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-20 11:31:56 UTC pid: 3744 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-20 11:32:13 UTC pid: 3744 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_0, started 25328)>

self: <Thread(Thread-39 (_readerthread), started daemon 30224)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 479, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

server-api: 2025-06-20 11:32:17 UTC pid: 3744 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_1, started 23600)>

self: <Thread(Thread-50 (_readerthread), started daemon 17700)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 544, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

server-api: 2025-06-20 12:00:32 UTC pid: 3744 - ThreadPoolExecutor-0_2 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: CreateRestPayloads.
Return code: 252
Output:
Importing library 'CreateRestPayloads' failed: ModuleNotFoundError: No module named 'CreateRestPayloads'

Traceback (most recent call last):

  None

PYTHONPATH:

  C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', 'CreateRestPayloads', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\CreateRestPayloads.libspec']' returned non-zero exit status 252.
server-api: 2025-06-20 12:00:33 UTC pid: 3744 - ThreadPoolExecutor-0_2 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdate.
Return code: 252
Output:
Importing library 'PostExecutionUpdate' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\vms\utility\PostExecutionUpdate.py", line 1, in <module>

    from acintegration.TestrailIntegration import TestrailIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\vms\utility

  c:\Alternative\Alternative_Physical_Channels_QA\vms\utility

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility', 'PostExecutionUpdate', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\24363dff.libspec']' returned non-zero exit status 252.
server-api: 2025-06-20 12:00:37 UTC pid: 3744 - Thread-5 - CRITICAL - robocorp_ls_core.jsonrpc.endpoint
===============================================================================
Slow request (already took: 8s). Showing thread dump.
================================= Thread Dump =================================

-------------------------------------------------------------------------------
 Thread <Thread(ThreadPoolExecutor-0_2, started 18968)>

self: <Thread(Thread-94 (_readerthread), started daemon 22132)>

 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1032, in _bootstrap
   self._bootstrap_inner()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1075, in _bootstrap_inner
   self.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1012, in run
   self._target(*self._args, **self._kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 93, in _worker
   work_item.run()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
   result = self.fn(*self.args, **self.kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\endpoint.py", line 265, in _call_checking_time
   return func(**kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 446, in _threaded_lint
   analysis_errors = code_analysis.collect_analysis_errors(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_analysis.py", line 390, in collect_analysis_errors
   collect_keywords(initial_completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 583, in collect_keywords
   _collect_from_context(completion_context, collector)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 544, in _collect_from_context
   _collect_libraries_keywords(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\collect_keywords.py", line 357, in _collect_libraries_keywords
   libspec_manager.get_library_doc_or_error(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1366, in get_library_doc_or_error
   error_msg = self._do_create_libspec_on_get(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1163, in _do_create_libspec_on_get
   error_creating = self._create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 918, in _create_libspec
   error_creating = self._cached_create_libspec(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
   self._subprocess_check_output(
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
   return subprocess.check_output(*args, **kwargs)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
   return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 550, in run
   stdout, stderr = process.communicate(input, timeout=timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1209, in communicate
   stdout, stderr = self._communicate(input, endtime, timeout)
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 1628, in _communicate
   self.stdout_thread.join(self._remaining_time(endtime))
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1149, in join
   self._wait_for_tstate_lock()
 File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\threading.py", line 1169, in _wait_for_tstate_lock
   if lock.acquire(block, timeout):

=============================== END Thread Dump ===============================

