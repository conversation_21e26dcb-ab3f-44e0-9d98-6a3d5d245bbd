server-api: 2025-06-20 12:24:28 UTC pid: 22516 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.3.lint.api']

server-api: 2025-06-20 12:24:28 UTC pid: 22516 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-20 12:24:28 UTC pid: 22516 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-20 12:24:28 UTC pid: 22516 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 22516

server-api: 2025-06-20 12:24:29 UTC pid: 22516 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-20 12:24:29 UTC pid: 22516 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-20 12:24:29 UTC pid: 22516 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-20 12:24:32 UTC pid: 22516 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.server_api.server
Error collecting Robocop errors (possibly an unsupported Robocop version is installed).

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\robocop_wrapper.py", line 16, in _import_robocop
    import robocop
ModuleNotFoundError: No module named 'robocop'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\server_api\server.py", line 475, in _threaded_lint
    collect_robocop_diagnostics(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\robocop_wrapper.py", line 33, in collect_robocop_diagnostics
    _import_robocop()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\robocop_wrapper.py", line 23, in _import_robocop
    import robocop  # @UnusedImport
    ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\__init__.py", line 1, in <module>
    from robocop import checkers
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\checkers\__init__.py", line 42, in <module>
    from robocop.utils import modules_from_paths, modules_in_current_dir
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\utils\__init__.py", line 5, in <module>
    from robocop.utils.file_types import FileType, FileTypeChecker
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\utils\file_types.py", line 12, in <module>
    from robocop.utils.misc import rf_supports_lang
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\libs\robocop_lib\robocop\utils\misc.py", line 17, in <module>
    from robot.variables.search import VariableIterator
ImportError: cannot import name 'VariableIterator' from 'robot.variables.search' (C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robot\variables\search.py)
