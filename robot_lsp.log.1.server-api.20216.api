server-api: 2025-06-19 12:25:19 UTC pid: 20216 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['-v', '--log-file=c:\\Alternative\\robot_lsp.log.1.api']

server-api: 2025-06-19 12:25:19 UTC pid: 20216 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

server-api: 2025-06-19 12:25:19 UTC pid: 20216 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

server-api: 2025-06-19 12:25:19 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkServerApiWithObserver IO language server. pid: 20216

server-api: 2025-06-19 12:25:21 UTC pid: 20216 - MainThread - INFO - robotframework_ls.impl.libspec_manager
User libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\user

server-api: 2025-06-19 12:25:21 UTC pid: 20216 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Builtins libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\builtins

server-api: 2025-06-19 12:25:21 UTC pid: 20216 - MainThread - INFO - robotframework_ls.impl.libspec_manager
Cache libspec dir: C:\Users\<USER>\.robotframework-ls\specs\v2\5c98395c_7.2.2\cache

server-api: 2025-06-19 12:26:18 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 11, method: hover

server-api: 2025-06-19 13:18:29 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 61, method: hover

server-api: 2025-06-19 13:18:42 UTC pid: 20216 - Thread-6 (_on_thread) - INFO - robocorp_ls_core.workspace
Unable to get mtime for: c:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-609 _ATM_DETAIL_Verify_ATM_Details_Using_ATM_Serial_Number.robot

server-api: 2025-06-19 13:19:47 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 81, method: hover

server-api: 2025-06-19 13:19:50 UTC pid: 20216 - ThreadPoolExecutor-0_1 - EXCEPTION - robotframework_ls.impl.libspec_manager
Error creating libspec: PostExecutionUpdateV2.
Return code: 252
Output:
Importing library 'PostExecutionUpdateV2' failed: ModuleNotFoundError: No module named 'acintegration'

Traceback (most recent call last):

  File "c:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in <module>

    from acintegration.QMetryIntegration import QMetryIntegration

PYTHONPATH:

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  c:\Alternative\Alternative_Physical_Channels_QA\common_utilities

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312

  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages



Try --help for usage information.



Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 1051, in _cached_create_libspec
    self._subprocess_check_output(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\libspec_manager.py", line 933, in _subprocess_check_output
    return subprocess.check_output(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 466, in check_output
    return run(*popenargs, stdout=PIPE, timeout=timeout, check=True,
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\subprocess.py", line 571, in run
    raise CalledProcessError(retcode, process.args,
subprocess.CalledProcessError: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-m', 'robot.libdoc', '--format', 'XML', '--specdocformat', 'RAW', '-P', 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities', 'PostExecutionUpdateV2', 'C:\\Users\\<USER>\\.robotframework-ls\\specs\\v2\\5c98395c_7.2.2\\user\\a3b2cdf1.libspec']' returned non-zero exit status 252.
server-api: 2025-06-19 14:07:17 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 169, method: hover

server-api: 2025-06-19 14:20:04 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 196, method: hover

server-api: 2025-06-19 14:28:54 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 230, method: code_action

server-api: 2025-06-19 14:30:24 UTC pid: 20216 - ThreadPoolExecutor-0_2 - INFO - robotframework_ls.impl.completion_context
Unable to find: ../common/DBUtility.robot (checked paths: ['c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DBUtility.robot', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\common\\DBUtility.robot', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\robotframework_ls\\common\\DBUtility.robot'])

server-api: 2025-06-19 14:56:51 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 302, method: code_action

server-api: 2025-06-19 14:56:51 UTC pid: 20216 - ThreadPoolExecutor-0_0 - EXCEPTION - robotframework_ls.impl.workspace_symbols


Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\workspace_symbols.py", line 197, in iter_symbols_caches
    for _uri, symbols_cache in workspace_indexer.iter_uri_and_symbols_cache(
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\robot_workspace.py", line 528, in iter_uri_and_symbols_cache
    symbols_cache = _compute_symbols_from_ast(ctx)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\robot_workspace.py", line 160, in _compute_symbols_from_ast
    test_info = list_tests(completion_context)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\code_lens.py", line 19, in list_tests
    completion_context.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\impl\completion_context.py", line 211, in check_cancelled
    self._monitor.check_cancelled()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\monitor.py", line 21, in check_cancelled
    raise JsonRpcRequestCancelled()
robocorp_ls_core.jsonrpc.exceptions.JsonRpcRequestCancelled: None
server-api: 2025-06-19 15:07:59 UTC pid: 20216 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 318, method: code_action

server-api: 2025-06-19 15:08:00 UTC pid: 20216 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.impl.robot_workspace
Timed out gathering information from workspace symbols (only partial information was collected). Consider enabling the 'robot.workspaceSymbolsOnlyForOpenDocs' setting.

server-api: 2025-06-19 15:08:00 UTC pid: 20216 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.impl.workspace_symbols
Timed out gathering information from workspace symbols (only partial information was collected). Consider enabling the 'robot.workspaceSymbolsOnlyForOpenDocs' setting.

