*** Settings ***
#Author Name               : <PERSON><PERSON><PERSON><PERSON>
#Email Address             : <EMAIL>

Default Tags                                        VMS HEALTHCHECK    ATM DETAILS
Suite Setup                                         Set up environment variables
Test Teardown                                       The user logs out of VMS

Documentation                                       Verify Search Bar Functionality Search For Empty Input

#***********************************EXTERNAL LIBRIRIES***************************************
Library                                             SeleniumLibrary
Library                                             OperatingSystem
Library                                             DatabaseLibrary

#***********************************PROJECT RESOURCES***************************************
Resource                                            ../../../common_utilities/Login.robot
Resource                                            ../../../common_utilities/Logout.robot
Resource                                            ../../keywords/VMSPage/ATMDetails.robot
Resource                                            ../../keywords/common/SetEnvironmentVariales.robot

*** Variables ***
${SEARCH_FIELD}    xpath=//*[@id="searchField"]
${TABLE_XPATH}     xpath=//*[@id="root"]/div/table

*** Keywords ***
Verify Search Bar Functionality Search For Empty Input
    [Arguments]  ${DOCUMENTATION}  ${TEST_ENVIRONMENT}  ${SEARCH_KEY}
    Set Test Documentation  ${DOCUMENTATION}

    Given The user logs into the VMS Web Application     ${TEST_ENVIRONMENT}

    When The user clicks on the ATM Details link

    And The user lands on the ATM Details pages

    Then The user searches FrontEnd for Empty Input     ${SEARCH_KEY}

#    And The user reads and compares Frontend ATM Details to the Backend ATM Details of the Existing ATM     ${SERIAL_NUMBER}

The user searches FrontEnd for Empty Input
    [Arguments]  ${SEARCH_KEY}

    # Click on the Search Field
    Click Element                                   ${SEARCH_FIELD}
    Log to console      --------------------------The user has clicked Search Element
    Sleep  5s

    #Enter empty search key on ATM Details Page
    Input Text    ${SEARCH_FIELD}    ${SEARCH_KEY}
    Log to console      --------------------------The user has input Empty Search Key

    Sleep  5s

    # For empty input, we expect to see ATM data (no filtering)
    # Verify that ATM table is still visible and contains data
    Wait Until Element Is Visible    ${TABLE_XPATH}//tbody//tr
    ${datarows}=    Get WebElements    ${TABLE_XPATH}//tbody//tr
    ${row_count}=   Get Length    ${datarows}
    Should Be True    ${row_count} > 0
    Log to console  -------------------------- Empty search shows ${row_count} ATM rows as expected

| *** Test Cases ***                   |        *** KEYWORDS ***           |           ***DOCUMENTATION***      |     ***TEST_ENVIRONMENT***   |     ***SEARCH_KEY***   |
| Verify Search Bar Functionality Search For Empty Input |  Verify Search Bar Functionality Search For Empty Input    | Verify Search Bar Functionality Search For Existing ATM  |      VMS_UAT             |      ${EMPTY}             |