remote_fs_observer: 2025-06-20 12:04:04 UTC pid: 27760 - MainThread - INFO - robocorp_ls_core.remote_fs_observer__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '-v']

remote_fs_observer: 2025-06-20 12:04:04 UTC pid: 27760 - MainThread - INFO - robocorp_ls_core.remote_fs_observer__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: <unable to get __version__> (<module 'robocorp_ls_core' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

remote_fs_observer: 2025-06-20 12:04:04 UTC pid: 27760 - MainThread - INFO - robocorp_ls_core.remote_fs_observer__main__
CPUs: 12

remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\FIS_API\\keywords\\csvLibrary.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\FIS_API\\keywords\\csvLibrary.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 2 - 0.3261382421785316', 'src_path': 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\azure-pipelines.yml'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\APCAccessToken.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\APCAccessToken.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\CompareTokens.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:44 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\CompareTokens.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:45 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\MicrosoftAuthCode.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:45 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\MicrosoftAuthCode.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:45 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\QmetryClass.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:45 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\QmetryClass.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:45 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\UiActions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:45 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\common_utilities\\UiActions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\keywords\\api\\Resources\\CreateCampaignAPI.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\keywords\\api\\Resources\\CreateCampaignAPI.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\keywords\\api\\Resources\\QmetryRobotFile.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\keywords\\api\\Resources\\QmetryRobotFile.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\keywords\\api\\Resources\\TOBEDELETED.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\keywords\\api\\Resources\\TOBEDELETED.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\MicrosoftAuthCode.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\MicrosoftAuthCode.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\PersonPOJO.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\PersonPOJO.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\PostExecutionUpdate.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\PostExecutionUpdate.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\SQLVariables.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\SQLVariables.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\UiActions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\UiActions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\Utility.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:54 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal\\utility\\Utility.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\CommonFunctions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\CommonFunctions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\RestRequestsMarshal.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\RestRequestsMarshal.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\RestRequestsUnMarshal.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 229, in write
    stream.flush()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\RestRequestsUnMarshal.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 228, in write
    stream.write(as_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Approve.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 228, in write
    stream.write(as_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Approve.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 228, in write
    stream.write(as_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Delete.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 228, in write
    stream.write(as_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Delete.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 228, in write
    stream.write(as_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetAllBins.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetAllBins.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetBinById.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetBinById.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetBinsByBintypeName.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetBinsByBintypeName.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetBinsToReview.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\GetBinsToReview.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Reactivate.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:55 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Reactivate.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Reject.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Reject.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\SearchBinsByNumber.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\SearchBinsByNumber.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Update.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Update.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Upload.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bins\\Upload.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\AddBinType.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\AddBinType.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\BinTypeAdd.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\BinTypeAdd.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\BinTypeUpdate.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\BinTypeUpdate.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\DeleteBinTypeByID.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\DeleteBinTypeByID.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\GetAllBinTypes.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\GetAllBinTypes.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\GetBinTypesById.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\GetBinTypesById.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\SearchBinTypeByName.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\bintypes\\SearchBinTypeByName.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\deviceversions\\ConfirmDownload.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\deviceversions\\ConfirmDownload.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\deviceversions\\DownloadBinTable.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\deviceversions\\DownloadBinTable.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetBinOverviewMetrics.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetBinOverviewMetrics.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetDownloadManagementInformation.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetDownloadManagementInformation.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetListOfServerVersions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetListOfServerVersions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetUploadManagementInformation.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:56 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\keywords\\controllers\\resources\\managementinformation\\GetUploadManagementInformation.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:57 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\utility\\SQLVariables.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:57 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\utility\\SQLVariables.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:57 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\utility\\Utils.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:04:57 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\future_fit_architecture_portal_BinTables\\utility\\Utils.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\Common_Functions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\Common_Functions.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\ConnectionTest.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\ConnectionTest.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\DatabaseUtility.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\DatabaseUtility.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\PostExecutionUpdateV2.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\PostExecutionUpdateV2.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\SQL.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:00 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\mtgla\\Utility\\SQL.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_210_Successful_Login_Redirects_to_Dashboard.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_218_Validate_Top_10_ATMs_with_the_Highest_Calls_This_Year.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_211_Validate_SLA_Status_per_Main_Vendor_This_Year.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_610_Validate_Calls_Logged_Against_Devices_This_Month.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_215_Validate_Top_10_ATMs_with_the_Highest_Calls_This_Week.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_214_Validate_Main_Calls_Logged_for_ATMs_Across_the_Country_This_Week.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_216_Validate_Calls_Logged_Against_Devices_This_Week.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_213_Validate_Top_10_ATMs_with_the_Highest_Calls_This_Month.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:02 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\tests\\DASHBOARD\\RAC29a_TC_217_Validate_SLA_Status_per_Main_Vendor_This_Week.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:30 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\PostExecutionUpdateV2.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:30 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\PostExecutionUpdateV2.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:31 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\PythonSqllite.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:31 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\PythonSqllite.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:31 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\SoapService.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:05:31 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\SoapService.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:10:24 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\VMSPage\\Dashboard.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:10:24 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\SQLVariables.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:10:24 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 9 - 0.8515117988153325', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\utility\\SQLVariables.py'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
remote_fs_observer: 2025-06-20 12:14:24 UTC pid: 27760 - Thread-4 - EXCEPTION - robocorp_ls_core.jsonrpc.streams
Failed to write message to output file {'command': 'on_change', 'on_change_id': '9912 - 8 - 0.193656389127021', 'src_path': 'c:\\Alternative\\Alternative_Physical_Channels_QA\\vms\\keywords\\common\\DatabaseConnector.robot'} - closed: False

Traceback (most recent call last):
  File "c:\Users\<USER>\.vscode\extensions\robocorp.robotframework-lsp-1.13.0\src\robotframework_ls\vendored\robocorp_ls_core\jsonrpc\streams.py", line 227, in write
    stream.write(content_len_bytes)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 738, in write
    return self._sock.send(b)
           ^^^^^^^^^^^^^^^^^^
ConnectionResetError: [WinError 10054] An existing connection was forcibly closed by the remote host
