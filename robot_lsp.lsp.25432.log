lsp: 2025-06-19 12:25:03 UTC pid: 25432 - MainThread - INFO - robotframework_ls.__main__
Arguments: ['--log-file=c:\\Alternative\\robot_lsp.log', '--verbose']

lsp: 2025-06-19 12:25:03 UTC pid: 25432 - MainThread - INFO - robotframework_ls.__main__
Python: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit (AMD64)] - lsp: 1.13.0 (<module 'robotframework_ls' from 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\__init__.py'>) - platform: win32 - sys.prefix: C:\Users\<USER>\AppData\Local\Programs\Python\Python312 - sys.executable: C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe

lsp: 2025-06-19 12:25:03 UTC pid: 25432 - MainThread - INFO - robotframework_ls.__main__
CPUs: 12

lsp: 2025-06-19 12:25:03 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.python_ls
Starting RobotFrameworkLanguageServer IO language server. pid: 25432

lsp: 2025-06-19 12:25:03 UTC pid: 25432 - MainThread - INFO - robotframework_ls.robotframework_ls_impl
Using watch implementation: watchdog (customize with ROBOTFRAMEWORK_LS_WATCH_IMPL environment variable)

lsp: 2025-06-19 12:25:03 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.remote_fs_observer_impl
Initializing Remote FS Observer with the following args: ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe', '-u', 'c:\\Users\\<USER>\\.vscode\\extensions\\robocorp.robotframework-lsp-1.13.0\\src\\robotframework_ls\\vendored\\robocorp_ls_core\\remote_fs_observer__main__.py', '--log-file=c:\\Alternative\\robot_lsp.log', '-v']

lsp: 2025-06-19 12:26:18 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 12, method: textDocument/hover

lsp: 2025-06-19 12:26:18 UTC pid: 25432 - ThreadPoolExecutor-0_1 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 12:29:55 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 23, method: textDocument/documentHighlight

lsp: 2025-06-19 12:29:55 UTC pid: 25432 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 12:30:58 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 30, method: textDocument/documentHighlight

lsp: 2025-06-19 12:30:58 UTC pid: 25432 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 12:48:04 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 31

lsp: 2025-06-19 12:48:06 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 34, method: textDocument/codeAction

lsp: 2025-06-19 13:17:23 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 60

lsp: 2025-06-19 13:18:29 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 83, method: textDocument/hover

lsp: 2025-06-19 13:18:29 UTC pid: 25432 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 13:18:42 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 88, method: textDocument/documentHighlight

lsp: 2025-06-19 13:18:42 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 86

lsp: 2025-06-19 13:18:42 UTC pid: 25432 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 13:19:07 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 103, method: textDocument/documentHighlight

lsp: 2025-06-19 13:19:07 UTC pid: 25432 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 13:19:44 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 127, method: textDocument/hover

lsp: 2025-06-19 13:19:47 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 139, method: textDocument/hover

lsp: 2025-06-19 13:19:47 UTC pid: 25432 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 13:19:53 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 146, method: textDocument/documentHighlight

lsp: 2025-06-19 13:19:53 UTC pid: 25432 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 14:07:17 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 237, method: textDocument/hover

lsp: 2025-06-19 14:07:17 UTC pid: 25432 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 14:20:03 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 273, method: textDocument/hover

lsp: 2025-06-19 14:20:03 UTC pid: 25432 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 14:20:25 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Ignoring notification for unknown method $/setTrace

lsp: 2025-06-19 14:26:01 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 290

lsp: 2025-06-19 14:26:03 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 287

lsp: 2025-06-19 14:26:03 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 293

lsp: 2025-06-19 14:28:54 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 317, method: textDocument/codeAction

lsp: 2025-06-19 14:28:54 UTC pid: 25432 - ThreadPoolExecutor-0_4 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 14:40:46 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 332

lsp: 2025-06-19 14:49:47 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 355

lsp: 2025-06-19 14:49:48 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 362

lsp: 2025-06-19 14:54:20 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 461, method: textDocument/hover

lsp: 2025-06-19 14:54:33 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 467, method: textDocument/hover

lsp: 2025-06-19 14:56:51 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 474, method: textDocument/codeAction

lsp: 2025-06-19 14:56:51 UTC pid: 25432 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 14:58:37 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 483

lsp: 2025-06-19 15:01:52 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 502, method: textDocument/codeLens

lsp: 2025-06-19 15:01:52 UTC pid: 25432 - ThreadPoolExecutor-0_6 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 15:07:59 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 520, method: textDocument/codeAction

lsp: 2025-06-19 15:07:59 UTC pid: 25432 - ThreadPoolExecutor-0_0 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 15:08:04 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 524, method: textDocument/hover

lsp: 2025-06-19 15:12:04 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.monitor
Cancelled: Message: id: 578, method: textDocument/codeLens

lsp: 2025-06-19 15:12:04 UTC pid: 25432 - ThreadPoolExecutor-0_5 - INFO - robotframework_ls.robotframework_ls_impl
Cancelled handling: <function RobotFrameworkLanguageServer._threaded_api_request at 0x0000013BFA50F560>

lsp: 2025-06-19 15:12:04 UTC pid: 25432 - MainThread - INFO - robocorp_ls_core.jsonrpc.endpoint
Received cancel notification for unknown message id 581

