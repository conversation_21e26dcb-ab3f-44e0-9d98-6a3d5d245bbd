<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-19T12:27:46.770036" rpa="false" schemaversion="5">
<suite id="s1" name="VMS Portal" source="C:\Alternative\Alternative_Physical_Channels_QA\vms\tests\ATM_DETAILS\RAC29a-TC-605_ATM_DETAIL_Verify_ATM_Details.robot">
<kw name="Set up environment variables" owner="SetEnvironmentVariales" type="SETUP">
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:52.955201" level="INFO">Environment variable 'UPLOAD_TEST_STEPS' set to value 'Yes'.</msg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.000000"/>
</kw>
<arg>'${UPLOAD_TEST_STEPS}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>UPLOAD_TEST_STEPS</arg>
<arg>${UPLOAD_TEST_STEPS}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${UPLOAD_TEST_STEPS} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:52.955201" level="INFO">Environment variable 'ROBOT_FILE_PATH' set to value 'C:\Alternative\Alternative_Physical_Channels_QA\vms\data\VMS_Portal_Data.xml'.</msg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.000000"/>
</kw>
<arg>'${ROBOT_FILE_PATH}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>ROBOT_FILE_PATH</arg>
<arg>${ROBOT_FILE_PATH}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${ROBOT_FILE_PATH} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:52.955201" level="INFO">Environment variable 'IS_HEADLESS_BROWSER' set to value 'No'.</msg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.000000"/>
</kw>
<arg>'${IS_HEADLESS_BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>IS_HEADLESS_BROWSER</arg>
<arg>${IS_HEADLESS_BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${IS_HEADLESS_BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.961889" level="INFO">Environment variable  does not exist.</msg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T12:27:52.961889" elapsed="0.000000"/>
</kw>
<arg>'${BASE_URL}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BASE_URL</arg>
<arg>${BASE_URL}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BASE_URL} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.006688"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:52.962916" level="INFO">Environment variable 'BROWSER' set to value 'edge'.</msg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T12:27:52.962916" elapsed="0.000000"/>
</kw>
<arg>'${BROWSER}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>BROWSER</arg>
<arg>${BROWSER}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable ${BROWSER} does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.961889" elapsed="0.001027"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Set Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:52.964916" level="INFO">Environment variable 'SUITE_DIRECTORY' set to value 'C:\Alternative\Alternative_Physical_Channels_QA\vms\data'.</msg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<doc>Sets an environment variable to a specified value.</doc>
<status status="PASS" start="2025-06-19T12:27:52.963917" elapsed="0.000999"/>
</kw>
<arg>'${SUITE_DIRECTORY}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>SUITE_DIRECTORY</arg>
<arg>${SUITE_DIRECTORY}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'SUITE_DIRECTORY', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.962916" elapsed="0.002997"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.965913" level="INFO">Environment variable called 'MS_USERNAME', does not exist.</msg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T12:27:52.965913" elapsed="0.000000"/>
</kw>
<arg>'${MS_USERNAME}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_USERNAME</arg>
<arg>${MS_USERNAME}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_USERNAME', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.965913" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.966959" level="INFO">Environment variable called 'MS_PASSWORD', does not exist.</msg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T12:27:52.966959" elapsed="0.000000"/>
</kw>
<arg>'${MS_PASSWORD}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>MS_PASSWORD</arg>
<arg>${MS_PASSWORD}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'MS_PASSWORD', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.966959" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${TEST_CASE_ID}' != ''">
<kw name="Get Test Case ID" owner="SetEnvironmentVariales">
<var>${TEST_CASE_ID}</var>
<arg>${TEST_CASE_ID}</arg>
<status status="NOT RUN" start="2025-06-19T12:27:52.967918" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Test Case ID: ${TEST_CASE_ID}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-19T12:27:52.967918" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T12:27:52.966959" elapsed="0.001418"/>
</branch>
<status status="PASS" start="2025-06-19T12:27:52.966959" elapsed="0.001418"/>
</if>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.969850" level="INFO">Environment variable called 'TEST_CASE_ID', does not exist.</msg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T12:27:52.969850" elapsed="0.000000"/>
</kw>
<arg>'${TEST_CASE_ID}' != ''</arg>
<arg>Set Environment Variable</arg>
<arg>TEST_CASE_ID</arg>
<arg>${TEST_CASE_ID}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>Environment variable called 'TEST_CASE_ID', does not exist.</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:52.968816" elapsed="0.002111"/>
</kw>
<status status="PASS" start="2025-06-19T12:27:52.955201" elapsed="0.015726"/>
</kw>
<test id="s1-t1" name="Validate Existance of All Frontend ATMs" line="38">
<kw name="ATM Existance Validation">
<kw name="Set Test Documentation" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.974023" level="INFO">Set test documentation to:
Validate Existance of All Frontend ATMs</msg>
<arg>${DOCUMENTATION}</arg>
<doc>Sets documentation for the current test case.</doc>
<status status="PASS" start="2025-06-19T12:27:52.974023" elapsed="0.000000"/>
</kw>
<kw name="Given The user logs into the VMS Web Application" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.975069" level="INFO">${system} = Windows</msg>
<var>${system}</var>
<arg>platform.system()</arg>
<arg>platform</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T12:27:52.975069" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nOperating System: ${system}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T12:27:52.975069" elapsed="0.001137"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------The user logs into VMS</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T12:27:52.976206" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="VMS Windows system Login" owner="Login">
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:52.976206" level="FAIL">Environment variable 'BASE_URL' does not exist.</msg>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="FAIL" start="2025-06-19T12:27:52.976206" elapsed="0.000000">Environment variable 'BASE_URL' does not exist.</status>
</kw>
<msg time="2025-06-19T12:27:52.976206" level="INFO">${url_exists_on_env_var} = False</msg>
<var>${url_exists_on_env_var}</var>
<arg>Get Environment Variable</arg>
<arg>BASE_URL</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T12:27:52.976206" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="${url_exists_on_env_var}">
<kw name="Get Environment Variable" owner="OperatingSystem">
<var>${BASE_URL}</var>
<arg>BASE_URL</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="NOT RUN" start="2025-06-19T12:27:52.976206" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T12:27:52.976206" elapsed="0.000000"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.979589" level="INFO">${BASE_URL} = VMS_UAT</msg>
<var>${BASE_URL}</var>
<arg>${URL}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T12:27:52.976206" elapsed="0.003383"/>
</kw>
<status status="PASS" start="2025-06-19T12:27:52.976206" elapsed="0.003383"/>
</branch>
<status status="PASS" start="2025-06-19T12:27:52.976206" elapsed="0.003383"/>
</if>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-19T12:27:52.996418" level="INFO">Property value fetched is:  https://vms.uat.absa.africa/</msg>
<msg time="2025-06-19T12:27:52.996418" level="INFO">${base_url} = https://vms.uat.absa.africa/</msg>
<var>${base_url}</var>
<arg>${BASE_URL}</arg>
<status status="PASS" start="2025-06-19T12:27:52.979589" elapsed="0.016829"/>
</kw>
<kw name="Begin Web test" owner="Login">
<kw name="Kill process" owner="Login">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.998441" level="INFO">${PROCESS_NAME_LowerCase} = edge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>"${PROCESS_NAME}".lower()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T12:27:52.997448" elapsed="0.000993"/>
</kw>
<if>
<branch type="IF" condition="'${PROCESS_NAME_LowerCase}' == 'edge'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.998441" level="INFO">${PROCESS_NAME_LowerCase} = msedge</msg>
<var>${PROCESS_NAME_LowerCase}</var>
<arg>msedge</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T12:27:52.998441" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-19T12:27:52.998441" elapsed="0.000000"/>
</branch>
<status status="PASS" start="2025-06-19T12:27:52.998441" elapsed="0.000000"/>
</if>
<kw name="Catenate" owner="BuiltIn">
<msg time="2025-06-19T12:27:52.999499" level="INFO">${handle} = msedge.exe</msg>
<var>${handle}</var>
<arg>SEPARATOR=.</arg>
<arg>${PROCESS_NAME_LowerCase}</arg>
<arg>exe</arg>
<doc>Catenates the given items together and returns the resulted string.</doc>
<status status="PASS" start="2025-06-19T12:27:52.999499" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>\nProcess to kill: ${handle}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-19T12:27:52.999499" elapsed="0.000994"/>
</kw>
<kw name="Run And Return Rc And Output" owner="OperatingSystem">
<msg time="2025-06-19T12:27:53.174744" level="INFO">Running command 'taskkill /F /IM msedge.exe 2&gt;&amp;1'.</msg>
<msg time="2025-06-19T12:27:53.922329" level="INFO">${rc_code} = 128</msg>
<msg time="2025-06-19T12:27:53.922329" level="INFO">${output} = ERROR: The process "msedge.exe" not found.</msg>
<var>${rc_code}</var>
<var>${output}</var>
<arg>taskkill /F /IM ${handle}</arg>
<doc>Runs the given command in the system and returns the RC and output.</doc>
<status status="PASS" start="2025-06-19T12:27:53.000493" elapsed="0.921836"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-19T12:27:53.922329" level="WARN">There was error during termination of process</msg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<arg>'${rc_code}' != '0'</arg>
<arg>Log</arg>
<arg>There was error during termination of process</arg>
<arg>WARN</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<arg>${BROWSER}</arg>
<status status="PASS" start="2025-06-19T12:27:52.997448" elapsed="0.924881"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:53.922329" level="INFO">${is_browser_browser} = No</msg>
<var>${is_browser_browser}</var>
<arg>IS_HEADLESS_BROWSER</arg>
<doc>Returns the value of an environment variable with the given name.</doc>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-19T12:27:53.922329" level="INFO">${is_headless_browser_type} = NO</msg>
<var>${is_headless_browser_type}</var>
<arg>${is_browser_browser}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<kw name="Convert To Upper Case" owner="String">
<msg time="2025-06-19T12:27:53.922329" level="INFO">${browser_name} = EDGE</msg>
<var>${browser_name}</var>
<arg>${BROWSER}</arg>
<doc>Converts string to upper case.</doc>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<kw name="Get Environment Variable" owner="OperatingSystem">
<msg time="2025-06-19T12:27:53.922329" level="INFO">${user_home} = C:\Users\<USER>\Users\AB038N8</msg>
<arg>Logged in User: ${user_home}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${browser_name}' == 'EDGE'">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T12:27:53.922329" level="INFO">${edgedriver_path} = C:/bin/msedgedriver.exe</msg>
<var>${edgedriver_path}</var>
<arg>C:/bin/msedgedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<kw name="Get Edge Driver Options" owner="Common_Functions">
<msg time="2025-06-19T12:27:53.922329" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000023648F68E30&gt;</msg>
<var>${edge_options}</var>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Get Edge Driver Options" owner="Common_Functions">
<var>${edge_options}</var>
<status status="NOT RUN" start="2025-06-19T12:27:53.929228" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-19T12:27:53.929228" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T12:27:53.922329" elapsed="0.006899"/>
</branch>
<branch type="ELSE">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-19T12:27:53.931262" level="INFO">${edge_options} = &lt;selenium.webdriver.edge.options.Options object at 0x0000023648F6A870&gt;</msg>
<var>${edge_options}</var>
<arg>sys.modules['selenium.webdriver'].EdgeOptions()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-19T12:27:53.930266" elapsed="0.000996"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T12:27:53.932064" level="INFO">${root} = C:\Users\<USER>\</msg>
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T12:27:53.931262" elapsed="0.000908"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T12:27:53.932170" level="INFO">${path} = Downloads</msg>
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T12:27:53.932170" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-19T12:27:53.933171" level="INFO">${downl_path} = C:\Users\<USER>\Downloads</msg>
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-19T12:27:53.932170" elapsed="0.001001"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<msg time="2025-06-19T12:27:53.933171" level="INFO">${prefs} = {'download.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.directory_upgrade': 'True', 'savefile.default_directory': 'C:\\Users\\<USER>\\Downloads', 'download.prompt_for_download': 'Fa...</msg>
<var>${prefs}</var>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="PASS" start="2025-06-19T12:27:53.933171" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${edge_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="PASS" start="2025-06-19T12:27:53.934442" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<msg time="2025-06-19T12:27:53.934442" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-19T12:27:53.936465" level="INFO">Opening browser 'edge' to base url 'about:blank'.</msg>
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${edge_options}</arg>
<arg>executable_path=${edgedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="PASS" start="2025-06-19T12:27:53.934442" elapsed="6.628780"/>
</kw>
<status status="PASS" start="2025-06-19T12:27:53.929228" elapsed="6.633994"/>
</branch>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="6.655426"/>
</if>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="6.655426"/>
</branch>
<branch type="ELSE">
<kw name="Set Variable" owner="BuiltIn">
<var>${chromedriver_path}</var>
<arg>C:/bin/chromedriver.exe</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${chrome_options}</var>
<arg>sys.modules['selenium.webdriver'].ChromeOptions()</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</kw>
<if>
<branch type="IF" condition="'${is_headless_browser_type}' == 'YES'">
<kw name="Set Variable" owner="BuiltIn">
<var>${BROWSER}</var>
<arg>headlesschrome</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--headless</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</branch>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</if>
<kw name="Set Variable" owner="BuiltIn">
<var>${root}</var>
<arg>${user_home}\\</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${path}</var>
<arg>Downloads</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<var>${downl_path}</var>
<arg>${root}${path}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.000000"/>
</kw>
<kw name="Create Dictionary" owner="BuiltIn">
<var>${prefs}</var>
<arg>useAutomationExtension=${FALSE}</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>download.directory_upgrade=True</arg>
<arg>download.default_directory=${downl_path}</arg>
<arg>savefile.default_directory=${downl_path}</arg>
<arg>download.prompt_for_download=False</arg>
<doc>Creates and returns a dictionary based on the given ``items``.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.582798" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_experimental_option</arg>
<arg>prefs</arg>
<arg>${prefs}</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>user-data-dir\=${user_home}/AppData/Local/Google/Chrome/User Data</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--disable-dev-shm-usage</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--ignore-certificate-errors</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--incognito</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<arg>${chrome_options}</arg>
<arg>add_argument</arg>
<arg>--no-sandbox</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${dc}</var>
<arg>sys.modules['selenium.webdriver'].DesiredCapabilities.CHROME</arg>
<arg>sys, selenium.webdriver</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Call Method" owner="BuiltIn">
<var>${Options}</var>
<arg>${ChromeOptions}</arg>
<arg>to_capabilities</arg>
<doc>Calls the named method of the given object with the provided arguments.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<kw name="Open Browser" owner="SeleniumLibrary">
<arg>about:blank</arg>
<arg>${BROWSER}</arg>
<arg>options=${chrome_options}</arg>
<arg>executable_path=${chromedriver_path}</arg>
<doc>Opens a new browser instance to the optional ``url``.</doc>
<status status="NOT RUN" start="2025-06-19T12:28:00.583347" elapsed="0.000000"/>
</kw>
<status status="NOT RUN" start="2025-06-19T12:28:00.577755" elapsed="0.005592"/>
</branch>
<status status="PASS" start="2025-06-19T12:27:53.922329" elapsed="6.661018"/>
</if>
<status status="PASS" start="2025-06-19T12:27:52.997448" elapsed="7.585899"/>
</kw>
<kw name="Load" owner="Login">
<kw name="Maximize Browser Window" owner="SeleniumLibrary">
<doc>Maximizes current browser window.</doc>
<status status="PASS" start="2025-06-19T12:28:00.583347" elapsed="0.069053"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Go To" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:00.657432" level="INFO">Opening url 'https://vms.uat.absa.africa/'</msg>
<arg>${URL}</arg>
<doc>Navigates the current browser window to the provided ``url``.</doc>
<status status="PASS" start="2025-06-19T12:28:00.657432" elapsed="5.904294"/>
</kw>
<arg>'${URL}' != '${EMPTY}'</arg>
<arg>Go To</arg>
<arg>${URL}</arg>
<arg>ELSE IF</arg>
<arg>'$${URL}' == '${EMPTY}'</arg>
<arg>Fail</arg>
<arg>Test URL not provided</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-19T12:28:00.652400" elapsed="5.909690"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T12:28:16.567109" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T12:28:06.562090" elapsed="10.005019"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:16.567109" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T12:28:16.669495" level="INFO">${element_count_1} = 0</msg>
<var>${element_count_1}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:16.572072" elapsed="0.097423"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_1} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T12:28:16.670523" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T12:28:26.672575" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T12:28:16.671521" elapsed="10.001054"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:26.672575" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T12:28:26.700972" level="INFO">${element_count_2} = 0</msg>
<var>${element_count_2}</var>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:26.672575" elapsed="0.028397"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_2} &gt; 0</arg>
<arg>Navigation.Run Keyword Until Success</arg>
<arg>Click Element</arg>
<arg>${MICROSOFT_PICK_ACCOUNT}</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T12:28:26.700972" elapsed="0.000990"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T12:28:31.702532" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T12:28:26.701962" elapsed="5.000570"/>
</kw>
<kw name="Get Element Count" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:31.703551" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T12:28:31.750764" level="INFO">${element_count_3} = 0</msg>
<var>${element_count_3}</var>
<arg>${MICROSOFT_EMAIL_INPUT}</arg>
<doc>Returns the number of elements matching ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:31.703551" elapsed="0.047213"/>
</kw>
<kw name="Run Keyword And Return If" owner="BuiltIn">
<arg>${element_count_3} &gt; 0</arg>
<arg>Login to Mocrosoft</arg>
<doc>Runs the specified keyword and returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-19T12:28:31.751754" elapsed="0.000000"/>
</kw>
<arg>${base_url}</arg>
<status status="PASS" start="2025-06-19T12:28:00.583347" elapsed="31.168407"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:31.937161" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T12:28:31.751754" elapsed="0.185407"/>
</kw>
<msg time="2025-06-19T12:28:31.937161" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T12:28:31.751754" elapsed="0.185407"/>
</kw>
<while condition="${User_Name_Element_Visible}">
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:31.939157" level="INFO">Typing text 'vmsadmin' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:31.938165" elapsed="0.415931"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T12:28:32.354096" elapsed="0.060802"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:32.416308" level="INFO">Typing text 'P@ssw0rd123' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:32.416308" elapsed="0.338706"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:32.756068" level="INFO">Clicking button 'name=btnLogon'.</msg>
<arg>${VMS_LOGIN_BTN}</arg>
<doc>Clicks the button identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:32.755014" elapsed="15.521626"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-19T12:28:50.277067" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-19T12:28:48.276640" elapsed="2.000427"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Element Should Be Visible" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:50.327000" level="INFO">Element 'name=txtUsername' is displayed.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Verifies that the element identified by ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T12:28:50.277067" elapsed="0.049933"/>
</kw>
<msg time="2025-06-19T12:28:50.327000" level="INFO">${User_Name_Element_Visible} = True</msg>
<var>${User_Name_Element_Visible}</var>
<arg>Element Should Be Visible</arg>
<arg>${VMS_USERNAME_INPUT}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-19T12:28:50.277067" elapsed="0.049933"/>
</kw>
<status status="PASS" start="2025-06-19T12:28:31.938165" elapsed="18.388835"/>
</iter>
<iter>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:50.327000" level="INFO">Typing text 'vmsadmin' into text field 'name=txtUsername'.</msg>
<arg>${VMS_USERNAME_INPUT}</arg>
<arg>${APPLICATION_USERNAME}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:50.327000" elapsed="0.288985"/>
</kw>
<kw name="Wait Until Element Is Visible" owner="SeleniumLibrary">
<arg>${VMS_PASSWORD_INPUT}</arg>
<doc>Waits until the element ``locator`` is visible.</doc>
<status status="PASS" start="2025-06-19T12:28:50.615985" elapsed="0.040856"/>
</kw>
<kw name="Input Text" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:50.657821" level="INFO">Typing text 'P@ssw0rd123' into text field 'name=txtPassword'.</msg>
<arg>${VMS_PASSWORD_INPUT}</arg>
<arg>${APPLICATION_PASSWORD}</arg>
<doc>Types the given ``text`` into the text field identified by ``locator``.</doc>
<status status="PASS" start="2025-06-19T12:28:50.657821" elapsed="0.244953"/>
</kw>
<kw name="Click Button" owner="SeleniumLibrary">
<msg time="2025-06-19T12:28:50.902774" level="INFO">Clicking button 'name=btnLogon'.</msg>
<errors>
<msg time="2025-06-19T12:27:46.826112" level="ERROR">Taking listener 'common_utilities/PostExecutionUpdateV2.py' into use failed: Importing listener 'common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'common_utilities\\PostExecutionUpdateV2'
Traceback (most recent call last):
  None
PYTHONPATH:
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-19T12:27:52.520021" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 197: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.530548" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 227: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.530548" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 258: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.530548" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 298: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.530548" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 343: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.530548" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 355: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.530548" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 398: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.530548" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 694: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.538448" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 716: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.540183" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 747: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.540338" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 783: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.542249" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 793: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.542249" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 804: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.542249" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 822: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.546451" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 829: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.548506" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 849: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.550505" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 876: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.552997" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 907: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.554648" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1013: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.557674" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1241: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.558867" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 1324: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.582971" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 117: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.582971" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\common_keywords.robot' on line 137: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.621897" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 338: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.621897" level="WARN">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\DatabaseConnector.robot' on line 372: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-19T12:27:52.935717" level="ERROR">Error in file 'C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\common\SetEnvironmentVariales.robot' on line 14: Importing library 'C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py' failed: ModuleNotFoundError: No module named 'acintegration'
Traceback (most recent call last):
  File "C:\Alternative\Alternative_Physical_Channels_QA\common_utilities\PostExecutionUpdateV2.py", line 1, in &lt;module&gt;
    from acintegration.QMetryIntegration import QMetryIntegration
PYTHONPATH:
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\robot.exe
  C:\Alternative
  C:\Alternative\Alternative_Physical_Channels_QA
  C:\Alternative\Alternative_Physical_Channels_QA\common_utilities
  C:\Alternative\Alternative_Physical_Channels_QA\vms\keywords
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python312.zip
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\DLLs
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312
  C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages</msg>
<msg time="2025-06-19T12:27:52.950406" level="WARN">Imported library 'C:\Alternative\Alternative_Physical_Channels_QA\future_fit_architecture_portal_BinTables\keywords\controllers\resources\RestRequestsUnMarshal.py' contains no keywords.</msg>
<msg time="2025-06-19T12:27:53.922329" level="WARN">There was error during termination of process</msg>
<msg time="2025-06-19T12:27:53.934442" level="WARN">exexcutable_path is being deprecated. Please use service to configure the driver's executable_path as per documentation.</msg>
<msg time="2025-06-19T12:28:16.567109" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T12:28:26.672575" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T12:28:31.703551" level="WARN">Keyword 'Get Element Count' found both from a custom library 'SeleniumLibrary' and a standard library 'XML'. The custom keyword is used. To select explicitly, and to get rid of this warning, use either 'SeleniumLibrary.Get Element Count' or 'XML.Get Element Count'.</msg>
<msg time="2025-06-19T12:28:54.691396" level="ERROR">Execution stopped by user.</msg>
</errors>
</robot>
